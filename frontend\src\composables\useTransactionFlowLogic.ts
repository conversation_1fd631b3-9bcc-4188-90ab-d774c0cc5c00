import { ref, computed, watch, onMounted, onUnmounted, type Ref, type ComputedRef } from 'vue';
import { useTransactionStore } from '@/stores/transaction';
import { useAuthStore } from '@/stores/auth';
import type { Transaction } from '@/types/transaction';
import { TransactionStatusEnum } from '@/types/transaction';
import type { MessageApi } from 'naive-ui'; // Corrected import for MessageApi

type ActionType = 'agree' | 'declarePayment' | 'confirmReceipt' | 'cancel' | 'dispute' | null;

export function useTransactionFlowLogic(propChatSessionId: Ref<string | null>, naiveMessage?: MessageApi) { // Changed to MessageApi
  const transactionStore = useTransactionStore();
  const authStore = useAuthStore();
  // Timer state
  const timeLeft = ref('00:00:00');
  let timerInterval: number | null = null;
  const isTimerCritical = ref(false);
  const isTimerExpired = ref(false);
  const isElapsedTimer = ref(false); // Track if this is an elapsed timer (counting up)
  // Modal and input state
  const showDeclarePaymentModal = ref(false);
  const paymentTrackingNumber = ref('');
  const showCancelModal = ref(false);
  const cancelReason = ref('');
  const showDisputeModal = ref(false);
  const disputeReason = ref('');

  const isActionLoading = ref<ActionType>(null);

  // Store-related computed properties
  const isLoading = computed(() => transactionStore.isLoading);
  const storeError = computed(() => transactionStore.error);
  const currentTransaction: ComputedRef<Transaction | null> = computed(() => transactionStore.currentTransaction);
  const currentTxStatus = computed(() => currentTransaction.value?.status); 
  const userId = computed(() => authStore.user?.id);
  const userRole = computed(() => transactionStore.userRole);

  // --- Timer Logic ---
  function startTimer() {
    stopTimer(); // Clear any existing timer
    
    const tx = currentTransaction.value;
    if (!tx) return;

    let dueDate: Date | null = null;
    let isElapsed = false;
    // Determine the appropriate due date based on transaction status
    if (tx.status === TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT && tx.paymentExpectedByPayer1) {
      dueDate = new Date(tx.paymentExpectedByPayer1);
    } else if (tx.status === TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION) {
      // Confirmation steps use elapsed timers (no due date, count up from when payment was declared)
      isElapsed = true;
      if (tx.paymentDeclaredAtPayer1) {
        dueDate = new Date(tx.paymentDeclaredAtPayer1);
      }
    } else if (tx.status === TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT && tx.paymentExpectedByPayer2) {
      dueDate = new Date(tx.paymentExpectedByPayer2);
    } else if (tx.status === TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION) {
      // Confirmation steps use elapsed timers (no due date, count up from when payment was declared)
      isElapsed = true;
      if (tx.paymentDeclaredAtPayer2) {
        dueDate = new Date(tx.paymentDeclaredAtPayer2);
      }
    }

    if (!dueDate) return;

    isElapsedTimer.value = isElapsed;

    if (timerInterval) {
      clearInterval(timerInterval);
      timerInterval = null;
    }

    // Define the timer update function
    const updateTimer = () => {
      // Safety check: verify transaction still exists and is valid
      if (!tx || !currentTransaction.value) {
        if (timerInterval) {
          clearInterval(timerInterval);
          timerInterval = null;
        }
        return;
      }

      const now = new Date();
      const diffMs = isElapsed ? now.getTime() - dueDate!.getTime() : dueDate!.getTime() - now.getTime();
      
      if (!isElapsed && diffMs <= 0) {
        // Countdown timer has expired, switch to elapsed timer showing total time since payment step started
        isTimerExpired.value = true;
        isElapsedTimer.value = true;
        
        // Calculate elapsed time from when the payment step actually started
        let paymentStepStartTime: Date | null = null;
        if (tx.status === TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT && tx.firstPayerDesignationTimestamp) {
          paymentStepStartTime = new Date(tx.firstPayerDesignationTimestamp);
        } else if (tx.status === TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT && tx.firstPaymentConfirmedByPayer2At) {
          paymentStepStartTime = new Date(tx.firstPaymentConfirmedByPayer2At);
        }

        if (paymentStepStartTime) {
          const totalElapsedMs = Math.max(0, now.getTime() - paymentStepStartTime.getTime());
          timeLeft.value = formatTime(totalElapsedMs, true);
        } else {
          // Fallback to showing time since deadline if step start time unavailable
          const elapsedMs = Math.max(0, Math.abs(diffMs));
          timeLeft.value = formatTime(elapsedMs, true);
        }
        // Timer has expired, so it's not in a critical state
        isTimerCritical.value = false;
      } else {
        isTimerExpired.value = false;
        timeLeft.value = formatTime(Math.max(0, Math.abs(diffMs)), isElapsed);
        // Set critical state only for active countdown timers with less than 30 minutes remaining
        isTimerCritical.value = !isElapsed && diffMs > 0 && diffMs <= 30 * 60 * 1000;
      }
    };

    // Update the timer immediately before starting the interval to prevent showing 00:00:00
    updateTimer();

    timerInterval = window.setInterval(updateTimer, 1000);
  }

  function formatTime(milliseconds: number, isElapsed: boolean = false): string {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    const prefix = isElapsed ? '+' : '';
    return `${prefix}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

function stopTimer() {
   if (timerInterval) clearInterval(timerInterval);
  timerInterval = null;
 }

  // --- Action Handling ---
  async function handleAction(
    actionType: ActionType,
    actionFn: () => Promise<any>,
    modalToClose?: Ref<boolean>,
    inputToClear?: Ref<string | null | string[]>
  ) {
    if (!currentTransaction.value) {
      if (naiveMessage) naiveMessage.error('No active transaction to perform this action.');
      else transactionStore.setError('No active transaction to perform this action.');
      return;
    }
    isActionLoading.value = actionType;
    transactionStore.clearError(); // Clear previous errors via store method

    try {
      await actionFn();
      // Success notification is handled by the store via setNotificationInstance
      // naiveMessage.success(successMessage); // No longer needed here if store handles it
      if (modalToClose) modalToClose.value = false;
      if (inputToClear) {
        if (typeof inputToClear.value === 'string') {
          inputToClear.value = '';
        } else if (inputToClear.value === null) {
          // it's already null, do nothing or handle as needed
        } else if (Array.isArray(inputToClear.value)) {
          inputToClear.value = []; // Example for array type if ever used
        } else {
            inputToClear.value = null; // Default for Ref<string | null>
        }
      }
    } catch (error: any) {
      // Error is set by the store method called in actionFn, or caught and set here if actionFn doesn't
      // if (naiveMessage && !transactionStore.error) naiveMessage.error(error.message || `Failed to ${actionType}.`);
      // Error should be displayed via the storeError computed property by the component
      console.error(`Error during ${actionType}:`, error.message);
    } finally {
      isActionLoading.value = null;
    }
  }
  const handleDeclarePayment = () => handleAction('declarePayment', () => transactionStore.declarePayment(paymentTrackingNumber.value || undefined), showDeclarePaymentModal, paymentTrackingNumber);
  
  const handleConfirmReceipt = () => handleAction('confirmReceipt', transactionStore.confirmReceipt);
  
  const handleCancelTransaction = () => {
    if (!cancelReason.value.trim()) {
        if (naiveMessage) naiveMessage.error('Cancellation reason cannot be empty.');
        else transactionStore.setError('Cancellation reason cannot be empty.');
        return;
    }
    return handleAction('cancel', () => transactionStore.cancelTransaction(cancelReason.value), showCancelModal, cancelReason);
  };

  const handleDisputeTransaction = () => {
    if (!disputeReason.value.trim()) {
        if (naiveMessage) naiveMessage.error('Dispute reason cannot be empty.');
        else transactionStore.setError('Dispute reason cannot be empty.');
        return;
    }
    return handleAction('dispute', () => transactionStore.disputeTransaction(disputeReason.value), showDisputeModal, disputeReason);
  };
  
  function clearStoreError() {
    transactionStore.clearError();
  }

  // --- Action Button Computed Properties ---
  const isTerminalState = computed(() => {
    if (!currentTransaction.value?.status) return false;
    const status = currentTransaction.value.status;
    return status === TransactionStatusEnum.COMPLETED ||
           status === TransactionStatusEnum.CANCELLED ||
           status === TransactionStatusEnum.DISPUTED;
  });
  const isUserTurnForPayment = computed(() => {
    if (!currentTransaction.value || !userId.value || !currentTransaction.value.agreedFirstPayerId) return false;
    const tx = currentTransaction.value;
    const me = userId.value;
    if (tx.status === TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT && me === tx.agreedFirstPayerId && !tx.paymentDeclaredAtPayer1) {
      return true;
    }
    const secondPayerId = tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyBProviderId : tx.currencyAProviderId;
    if (tx.status === TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT && me === secondPayerId && !tx.paymentDeclaredAtPayer2) {
      return true;
    }
    return false;
  });

  const isUserTurnForConfirmation = computed(() => {
    if (!currentTransaction.value || !userId.value || !currentTransaction.value.agreedFirstPayerId) return false;
    const tx = currentTransaction.value;
    const me = userId.value;
    const secondPayerId = tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyBProviderId : tx.currencyAProviderId;
    if (tx.status === TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION && me === secondPayerId && !!tx.paymentDeclaredAtPayer1 && !tx.firstPaymentConfirmedByPayer2At) {
      return true;
    }
    if (tx.status === TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION && me === tx.agreedFirstPayerId && !!tx.paymentDeclaredAtPayer2 && !tx.secondPaymentConfirmedByPayer1At) {
      return true;
    }
    return false;
  });

  const canCancel = computed(() => {
    if (!currentTransaction.value || !userId.value) return false;
    const tx = currentTransaction.value;
    // Allow cancellation if not in a terminal state by any participant
    return !isTerminalState.value && 
           (userId.value === tx.currencyAProviderId || userId.value === tx.currencyBProviderId);
  });

  const canDispute = computed(() => {
    if (!currentTransaction.value || !userId.value) return false;
    const tx = currentTransaction.value;
    // Allow dispute if not already disputed, and not completed/cancelled by any participant
    return tx.status !== TransactionStatusEnum.DISPUTED && 
           tx.status !== TransactionStatusEnum.COMPLETED && 
           tx.status !== TransactionStatusEnum.CANCELLED &&
           (userId.value === tx.currencyAProviderId || userId.value === tx.currencyBProviderId);  });

  const isCurrentActionBlockedByTimer = computed(() => {
    // User requirement: "no action ever should be banned during the timer runs out"
    // Timer expiration should not block any actions, only show warnings
    return false;
  });

  // --- Lifecycle and Watchers ---
  onMounted(async () => {
    if (propChatSessionId.value && userId.value) {
      await transactionStore.fetchTransactionByChatSessionId(propChatSessionId.value, userId.value);
    }
    startTimer();
  });

  onUnmounted(() => {
    stopTimer();
    if (timerInterval) {
      clearInterval(timerInterval);
      timerInterval = null;
    }
    // Consider if transactionStore.clearTransaction() is needed here or if ChatView handles it.
  });

  watch(propChatSessionId, async (newId, oldId) => {
    if (newId && newId !== oldId && userId.value) {
      stopTimer();
      await transactionStore.fetchTransactionByChatSessionId(newId, userId.value);
    } else if (!newId) {
      stopTimer();
      transactionStore.clearTransaction();
    }
  });
  
  watch(() => currentTransaction.value?.actionDeadline, (newDeadline) => {
    stopTimer();
    if (newDeadline) {
      startTimer();
    } else {
      timeLeft.value = '...';
    }
  }, { immediate: false }); 
  watch(() => currentTransaction.value?.status, (newStatus, oldStatus) => {
    if (newStatus !== oldStatus) {
      showDeclarePaymentModal.value = false;
      paymentTrackingNumber.value = '';
      showCancelModal.value = false;
      cancelReason.value = '';
      showDisputeModal.value = false;
      disputeReason.value = '';
      startTimer(); 
    }
  });

  // Expose reactive state and methods
  return {    // State
    currentTransaction,
    isLoading,
    storeError, // Export storeError instead of error
    // isUserTurn, // REMOVED - use more specific checks or userRole
    currentTxStatus, // Export currentTxStatus
    userId,
    userRole, // Export userRole
    timeLeft,
    isTimerCritical,
    isTimerExpired,
    isElapsedTimer,    isActionLoading,
    // Modal State
    showDeclarePaymentModal,
    paymentTrackingNumber,
    showCancelModal,
    cancelReason,
    showDisputeModal,
    disputeReason,
    // Action Button Computeds
    isTerminalState,
    // isUserTurnToAgree, // REMOVED
    isUserTurnForPayment,
    isUserTurnForConfirmation,
    canCancel,
    canDispute,
    isCurrentActionBlockedByTimer,
    // Methods
    // handleAgreeToTerms, // REMOVED
    handleDeclarePayment,
    handleConfirmReceipt,
    handleCancelTransaction,
    handleDisputeTransaction,
    clearStoreError,
    // Modal Openers
    openDeclarePaymentModal: () => { paymentTrackingNumber.value = ''; showDeclarePaymentModal.value = true; },
    openCancelModal: () => { cancelReason.value = ''; showCancelModal.value = true; },
    openDisputeModal: () => { disputeReason.value = ''; showDisputeModal.value = true; },
  };
}