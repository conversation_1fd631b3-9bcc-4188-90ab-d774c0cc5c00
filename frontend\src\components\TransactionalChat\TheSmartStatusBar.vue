<script setup lang="ts">
import { computed } from 'vue'
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore'
import { useAuthStore } from '@/stores/auth'
import { usePayerNegotiationStore } from '@/stores/payerNegotiation'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const transactionalChatStore = useTransactionalChatStore()

// Timer formatting
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  if (minutes > 0) {
    return t('transactionalChat.timer.minutes', { count: minutes }, minutes)
  } else {
    return t('transactionalChat.timer.seconds', { count: remainingSeconds }, remainingSeconds)
  }
}

// Computed properties

const currentStep = computed(() => transactionalChatStore.currentStep)
const currentStepIndex = computed(() => transactionalChatStore.currentStepIndex)
const totalSteps = computed(() => transactionalChatStore.totalSteps)
const timer = computed(() => transactionalChatStore.timer)
const otherUser = computed(() => transactionalChatStore.otherUser)
const transactionDetails = computed(() => transactionalChatStore.transactionDetails)

const stepTitle = computed(() => {
  const step = currentStep.value
  const otherUserName = otherUser.value?.name || 'User'
  const authStore = useAuthStore()
  const payerNegotiationStore = usePayerNegotiationStore()
  const currentUserId = authStore.user?.id
  
  // Get negotiation details to determine who is the first payer
  const negotiation = payerNegotiationStore.currentNegotiation
  const firstPayerId = negotiation?.finalizedPayerId
  
  // Determine names and amounts based on who is the first payer
  let firstPayerName = 'User'
  let firstPayerAmount = ''
  let firstPayerCurrency = ''
  
  if (firstPayerId && transactionDetails.value) {
    // Determine if current user is the first payer
    const isCurrentUserFirstPayer = firstPayerId === currentUserId
    if (isCurrentUserFirstPayer) {
      firstPayerName = t('common.you') // Use translation for "You"
      firstPayerAmount = String(transactionDetails.value.amountToSend ?? '')
      firstPayerCurrency = transactionDetails.value.currencyFrom
    } else {
      firstPayerName = otherUserName
      // If other user is first payer, they send what current user receives
      firstPayerAmount = String(transactionDetails.value.amountToReceive ?? '')
      firstPayerCurrency = transactionDetails.value.currencyTo
    }
  } else {
    // Fallback to default logic if negotiation data not available
    firstPayerAmount = String(transactionDetails.value?.amountToSend ?? '')
    firstPayerCurrency = transactionDetails.value?.currencyFrom || 'CAD'
  }
  
  switch (step?.key) {
    case 'waitingPayer1':
      return t('transactionalChat.steps.waitingPayer1', { 
        name: firstPayerName, 
        amount: `${firstPayerAmount} ${firstPayerCurrency}` 
      })
    case 'confirmReceipt':
      return t('transactionalChat.steps.confirmReceipt')

    case 'yourTurnToPay': {
      const userAmount = String(transactionDetails.value?.amountToSend ?? '')
      const userCurrency = transactionDetails.value?.currencyFrom || 'CAD'
      return t('transactionalChat.steps.yourTurnToPay', { amount: `${userAmount} ${userCurrency}` })
    }
    case 'waitingPayer2':
      return t('transactionalChat.steps.waitingPayer2', { name: otherUserName })
    default:
      return step ? t(step.titleKey) : ''
  }
})

const timerText = computed(() => {
  if (!timer.value.isActive) return ''
  
  if (timer.value.remainingSeconds <= 0) {
    return t('transactionalChat.timer.expired')
  }
  
  return t('transactionalChat.statusBar.timeLeft', { 
    time: formatTime(timer.value.remainingSeconds) 
  })
})

const timerColor = computed(() => {
  if (!timer.value.isActive) return 'var(--tc-text-secondary)'
  
  const seconds = timer.value.remainingSeconds
  if (seconds <= 60) return 'var(--tc-timer-danger)'      // Last minute - red
  if (seconds <= 300) return 'var(--tc-timer-warning)'    // Last 5 minutes - yellow
  return 'var(--tc-timer-normal)'                         // Normal - gray
})



const isUsersTurn = computed(() => transactionalChatStore.isUsersTurn)
const isStatusBarShrunk = computed(() => transactionalChatStore.isStatusBarShrunk)


// Steps that require a timer in shrunk mode
const stepsWithTimer = ['makePayment', 'confirmReceipt', 'makeSecondPayment', 'confirmFirstPaymentReceipt']

const showShrunkTimer = computed(() => {
  const shrunk = isStatusBarShrunk.value
  const timerActive = timer.value.isActive
  const stepKey = currentStep.value?.key
  const show = shrunk && timerActive && stepsWithTimer.includes(stepKey)
  return show
})
import { watch } from 'vue'



</script>

<template>
  <div
    class="status-bar"
    :class="isStatusBarShrunk ? 'status-bar--shrunk' : 'status-bar--expanded'"
    data-testid="smart-status-bar"
  >
    <!-- Shrunk (compact) mode -->
  <template v-if="isStatusBarShrunk">
      <div class="shrunk-bar">
        <span class="shrunk-step">
          {{ t('transactionalChat.statusBar.step', { current: currentStepIndex + 1, total: totalSteps }) }}:
        </span>
  <span class="shrunk-title">{{ stepTitle }}</span>
        <span v-if="showShrunkTimer" class="shrunk-timer" :style="{ color: timerColor }">
          • {{ timerText }}
        </span>
      </div>
    </template>
    <!-- Expanded (full) mode -->
    <template v-else>
      <!-- Progress Steps -->
      <div
        class="progress-container"
        data-testid="progress-container"
      >
        <div class="progress-steps">
          <div
            v-for="stepIndex in totalSteps"
            :key="stepIndex"
            class="progress-step"
            :class="{
              'step-completed': stepIndex <= currentStepIndex,
              'step-active': stepIndex === currentStepIndex + 1,
              'step-pending': stepIndex > currentStepIndex + 1
            }"
            :data-testid="`progress-step-${stepIndex}`"
          >
            <div class="step-circle">
              <span v-if="stepIndex <= currentStepIndex" class="step-check">✓</span>
              <span v-else class="step-number">{{ stepIndex }}</span>
            </div>
            <div
              v-if="stepIndex < totalSteps"
              class="step-line"
              :class="{
                'line-completed': stepIndex < currentStepIndex,
                'line-active': stepIndex === currentStepIndex,
                'line-pending': stepIndex > currentStepIndex
              }"
            ></div>
          </div>
        </div>
      </div>
      <!-- Status Info -->
      <div
        class="status-info"
        data-testid="status-info"
      >
        <!-- Main Status Text -->
        <div class="status-main">
          <h2
            class="status-title"
            :class="{ 'user-turn': isUsersTurn }"
            data-testid="status-title"
          >
            {{ stepTitle }}
          </h2>
          <!-- Step Counter -->
          <p
            class="step-counter"
            data-testid="step-counter"
          >
            {{ t('transactionalChat.statusBar.step', {
              current: currentStepIndex + 1,
              total: totalSteps
            }) }}
          </p>
        </div>
        <!-- Timer (if active) -->
        <div
          v-if="timer.isActive"
          class="timer-container"
          :style="{ color: timerColor }"
          data-testid="timer-container"
        >
          <span class="timer-text">{{ timerText }}</span>
          <div
            class="timer-pulse"
            :class="{
              'pulse-danger': timer.remainingSeconds <= 60,
              'pulse-warning': timer.remainingSeconds <= 300 && timer.remainingSeconds > 60,
              'pulse-normal': timer.remainingSeconds > 300
            }"
          ></div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped>

.smart-status-bar {
  background-color: var(--tc-bg-status);
  border-bottom: 1px solid var(--tc-border-light);
  padding: 16px;
  transition: all 0.25s cubic-bezier(0.4,0,0.2,1);

.smart-status-bar.shrunk {
  padding: 6px 14px;
  min-height: 36px;
  background: var(--tc-bg-status);
  border-bottom: 1px solid var(--tc-border-light);
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
  display: flex;
  align-items: center;
  font-size: 14px;
  z-index: 10;
}

.shrunk-bar {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.shrunk-step {
  font-weight: 600;
  color: var(--tc-primary);
  margin-right: 4px;
}
.shrunk-title {
  font-weight: 500;
  color: var(--tc-text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60vw;
}
.shrunk-timer {
  font-size: 13px;
  font-weight: 500;
  margin-left: 6px;
  color: var(--tc-timer-normal);
}
}

/* Progress Steps */
.progress-container {
  margin-bottom: 16px;
}

.progress-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  max-width: 100%;
  overflow-x: auto;
  padding: 0 4px;
}

.progress-step {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  min-width: 0;
}

.step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  flex-shrink: 0;
  z-index: 2;
  position: relative;
}

/* Step states */
.step-completed .step-circle {
  background-color: var(--tc-step-complete);
  color: var(--tc-text-white);
}

.step-active .step-circle {
  background-color: var(--tc-step-active);
  color: var(--tc-text-white);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

.step-pending .step-circle {
  background-color: var(--tc-step-pending);
  color: var(--tc-text-muted);
}

.step-check {
  font-size: 16px;
}

.step-number {
  font-size: 12px;
}

/* Step lines */
.step-line {
  height: 2px;
  flex: 1;
  margin: 0 8px;
  transition: background-color 0.3s ease;
}

.line-completed {
  background-color: var(--tc-step-complete);
}

.line-active {
  background: linear-gradient(to right, var(--tc-step-complete), var(--tc-step-active));
}

.line-pending {
  background-color: var(--tc-step-line);
}

/* Status Info */
.status-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.status-main {
  flex: 1;
  min-width: 0;
}

.status-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--tc-text-primary);
  line-height: 1.4;
  word-wrap: break-word;
}

.status-title.user-turn {
  color: var(--tc-primary);
}

.step-counter {
  font-size: 12px;
  color: var(--tc-text-muted);
  margin: 0;
}

/* Timer */
.timer-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.timer-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.timer-pulse {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 0.8;
}

.pulse-normal {
  background-color: var(--tc-timer-normal);
  animation: pulse-normal 2s infinite;
}

.pulse-warning {
  background-color: var(--tc-timer-warning);
  animation: pulse-warning 1.5s infinite;
}

.pulse-danger {
  background-color: var(--tc-timer-danger);
  animation: pulse-danger 1s infinite;
}

@keyframes pulse-normal {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 1; }
}

@keyframes pulse-warning {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

@keyframes pulse-danger {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .smart-status-bar {
    padding: 12px;
  }
  
  .progress-steps {
    padding: 0 2px;
  }
  
  .step-circle {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .step-check {
    font-size: 14px;
  }
  
  .step-number {
    font-size: 11px;
  }
  
  .status-title {
    font-size: 15px;
  }
  
  .step-counter {
    font-size: 11px;
  }
  
  .timer-text {
    font-size: 13px;
  }
  
  .status-info {
    gap: 12px;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .step-line {
    margin: 0 4px;
  }
  
  .step-circle {
    width: 24px;
    height: 24px;
    font-size: 11px;
  }
  
  .step-check {
    font-size: 12px;
  }
  
  .step-number {
    font-size: 10px;
  }
}

/* RTL Support */
[dir="rtl"] .progress-steps {
  direction: rtl;
}

[dir="rtl"] .status-info {
  direction: rtl;
}

[dir="rtl"] .status-main {
  text-align: right;
}

[dir="rtl"] .timer-container {
  flex-direction: row-reverse;
}
</style>
